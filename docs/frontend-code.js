/**
 * Frontend Code for /api3 Unified Cost and Quota Management System
 *
 * UPDATED FOR /API3 SYSTEM:
 * - All API endpoints updated from /api2 to /api3
 * - Enhanced EagleView quota enforcement (fixes critical security issue)
 * - Improved error handling for HTTP 402 (insufficient quota) responses
 * - Added cost and remaining quota display in purchase confirmations
 * - Consistent quota checking across all imagery providers
 *
 * Key Changes:
 * 1. EagleView purchases now enforce quota limits (was bypassed in /api2)
 * 2. Enhanced error messages for quota-related failures
 * 3. Real-time quota updates after purchases
 * 4. Unified cost management across Nearmap, EagleView, and Pictometry
 */

import UtilityLib from "./utility/utilitylib.js";
import subscription from '../../../core/js/components/subscription.js';




L.TileLayer.Masked = L.TileLayer.extend({
  initialize(urlTemplate, options) {
    this._urlTemplate  = urlTemplate;
    this._tileBounds   = options.tileBounds;             // L.LatLngBounds
    this._layerMinZoom = options.minZoom   ?? -Infinity; // existing zoom limits
    this._layerMaxZoom = options.maxZoom   ??  Infinity;
    L.TileLayer.prototype.initialize.call(this, urlTemplate, options);
  },

  onAdd(map) {
    this._origMin = map.getMinZoom();
    this._origMax = map.getMaxZoom();
    map.setMinZoom(this._layerMinZoom);
    map.setMaxZoom(this._layerMaxZoom);
    return L.TileLayer.prototype.onAdd.call(this, map);
  },

  onRemove(map) {
    L.TileLayer.prototype.onRemove.call(this, map);
    map.setMinZoom(this._origMin);
    map.setMaxZoom(this._origMax);
  },

  createTile(coords, done) {
    const size   = this.getTileSize();
    const canvas = L.DomUtil.create('canvas');
    canvas.width  = size.x;
    canvas.height = size.y;
    const ctx     = canvas.getContext('2d');

    // ▶︎ 1) Compute this tile’s LatLng bounds
    const nwPoint = coords.scaleBy(size);
    const sePoint = nwPoint.add(size);
    const nw = this._map.unproject(nwPoint, coords.z);
    const se = this._map.unproject(sePoint, coords.z);
    const tileLatLngBounds = L.latLngBounds(se, nw);

    // ▶︎ 2) If no intersection with your AOI, return transparent canvas immediately
    if (!tileLatLngBounds.intersects(this._tileBounds)) {
      // optional: stroke the tile border so you still see the grid
      //ctx.strokeStyle = 'rgba(255,0,0,0.3)';
      //ctx.lineWidth   = 1;
      //ctx.strokeRect(0, 0, size.x, size.y);
      done(null, canvas);
      return canvas;
    }

    // ▶︎ 3) Otherwise load & draw the masked tile
    const img = new Image();
    if (this.options.crossOrigin) img.crossOrigin = '';
    L.DomEvent.on(img, 'load', () => {
      // draw full tile
      ctx.drawImage(img, 0, 0, size.x, size.y);

      // clip to AOI polygon
      const corners = [
        this._tileBounds.getNorthWest(),
        this._tileBounds.getNorthEast(),
        this._tileBounds.getSouthEast(),
        this._tileBounds.getSouthWest()
      ];
      ctx.save();
      ctx.beginPath();
      corners.forEach((latlng, i) => {
        const pw     = this._map.project(latlng, coords.z);
        const origin = coords.scaleBy(size);
        const p      = pw.subtract(origin);
        i === 0 ? ctx.moveTo(p.x, p.y) : ctx.lineTo(p.x, p.y);
      });
      ctx.closePath();
      ctx.globalCompositeOperation = 'destination-in';
      ctx.fill();
      ctx.restore();

      // stroke tile border
      //ctx.strokeStyle = 'rgba(255,0,0,0.5)';
      //ctx.lineWidth   = 1;
      //ctx.strokeRect(0, 0, size.x, size.y);

      done(null, canvas);
    });
    L.DomEvent.on(img, 'error', L.bind(this._tileOnError, this, done, canvas));

    // let Leaflet substitute all tokens ({z}/{x}/{y}/{layer}/{tileMatrixSet})
    img.src = this.getTileUrl(coords);
    return canvas;
  }
});

// factory helper
L.tileLayer.masked = function(urlTemplate, options) {
  return new L.TileLayer.Masked(urlTemplate, options);
};
L.TileLayer.MaskedOffscreen = L.TileLayer.extend({
  initialize(urlTemplate, options = {}) {
    this._urlTemplate  = urlTemplate;
    this._tileBounds   = options.tileBounds;             // L.LatLngBounds
    this._layerMinZoom = options.minZoom   ?? -Infinity;
    this._layerMaxZoom = options.maxZoom   ??  Infinity;
    L.TileLayer.prototype.initialize.call(this, urlTemplate, options);
  },

  onAdd(map) {
    this._origMin = map.getMinZoom();
    this._origMax = map.getMaxZoom();
    map.setMinZoom(this._layerMinZoom);
    map.setMaxZoom(this._layerMaxZoom);
    return L.TileLayer.prototype.onAdd.call(this, map);
  },

  onRemove(map) {
    L.TileLayer.prototype.onRemove.call(this, map);
    map.setMinZoom(this._origMin);
    map.setMaxZoom(this._origMax);
  },

  createTile(coords, done) {
    /* ---------- 1. Create the final <img> that Leaflet will keep ---------- */
    const img = document.createElement('img');
    img.className = 'leaflet-tile';          // pick up Leaflet’s CSS
    if (this.options.crossOrigin) img.crossOrigin = '';

    /* ---------- 2. Early‑out if tile is outside AOI ---------- */
    const size   = this.getTileSize();
    const nwPt   = coords.scaleBy(size);
    const sePt   = nwPt.add(size);
    const nwLL   = this._map.unproject(nwPt, coords.z);
    const seLL   = this._map.unproject(sePt, coords.z);
    const tileLLB = L.latLngBounds(seLL, nwLL);

    if (!tileLLB.intersects(this._tileBounds)) {
      // Transparent 1×1 PNG data‑URI keeps things super small
      img.src =
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAOXZPNsAAAAASUVORK5CYII=';
      done(null, img);
      return img;
    }

    /* ---------- 3. Fetch the raw tile image ---------- */
    const raw = new Image();
    if (this.options.crossOrigin) raw.crossOrigin = '';
    raw.onload = async () => {
      /* ----- 4. Draw + mask on an off‑screen canvas ----- */
      // Prefer real OffscreenCanvas (runs in worker if you move this code),
      // else fall back to hidden <canvas>.
      const off =
        typeof OffscreenCanvas !== 'undefined'
          ? new OffscreenCanvas(size.x, size.y)
          : Object.assign(document.createElement('canvas'), {
              width: size.x,
              height: size.y
            });

      const ctx = off.getContext('2d');
      ctx.drawImage(raw, 0, 0, size.x, size.y);

      // Clip to AOI polygon
      const corners = [
        this._tileBounds.getNorthWest(),
        this._tileBounds.getNorthEast(),
        this._tileBounds.getSouthEast(),
        this._tileBounds.getSouthWest()
      ];
      ctx.save();
      ctx.beginPath();
      corners.forEach((ll, i) => {
        const pw = this._map.project(ll, coords.z);
        const origin = coords.scaleBy(size);
        const p = pw.subtract(origin);
        i === 0 ? ctx.moveTo(p.x, p.y) : ctx.lineTo(p.x, p.y);
      });
      ctx.closePath();
      ctx.globalCompositeOperation = 'destination-in';
      ctx.fill();
      ctx.restore();

      /* ----- 5. Snapshot canvas → blob / data‑URL → img.src ----- */
      const assignSrc = (url) => {
        img.src = url;
        // call done when *this* img finishes loading the masked pixels
        img.onload = () => done(null, img);
        img.onerror = (e) => done(e, img);
      };

      if (off.convertToBlob) {
        // OffscreenCanvas path
        const blob = await off.convertToBlob();
        assignSrc(URL.createObjectURL(blob));
      } else {
        // Fallback <canvas>
        assignSrc(off.toDataURL('image/png'));
      }
    };

    raw.onerror = (e) => done(e, img);
    raw.src = this.getTileUrl(coords);
    return img; // Leaflet inserts this immediately; pixels arrive once ready
  }
});

/* factory helper */
L.tileLayer.maskedOffscreen = function (urlTempl, opts) {
  return new L.TileLayer.MaskedOffscreen(urlTempl, opts);
};



export default {
    template: /*HTML*/`<div>
    <div id="provider-switch" style="margin-bottom:10px;">
        <v-btn-toggle v-model="currentBaseLayer" mandatory>
            <!--v-btn small color="white" class="stop-caps normal-spacing px-0" :value="0">
                Road
            </v-btn-->

            <v-btn small color="white" class="stop-caps normal-spacing px-0" :value="1">
                Google
            </v-btn>

           <!-- <v-btn small color="white" class="stop-caps normal-spacing px-0" :value="2">
                Bing
            </v-btn> -->
            <!--v-btn small color="white" class="stop-caps normal-spacing px-0" :value="3">
                Apple
            </v-btn-->
            <v-btn small color="white" class="stop-caps normal-spacing" @click="clickHighRes(currentBaseLayer)" :value="4">
                High Resolution
            </v-btn>
           <v-btn id="highres-date" v-if="tileUrl!=null" small color="white" class="stop-caps normal-spacing" @click="revertToLoaded" :value="4">
                Imagery Date: {{tileDateDisplay}}
            </v-btn>
        </v-btn-toggle>
        <!--div id="highres-date" v-if="currentBaseLayer==4 && tileUrl!=null">
            Imagery Date: {{tileDateDisplay}}
        </div-->
    </div>
      <v-dialog overlay-opacity="0" id="exhausted-dialog" v-if="exhaustedDialog" v-model="exhaustedDialog" persistent
              max-width="400">
        <v-card>
            <v-card-title>
                Add High Resolution Imagery
            </v-card-title>
            <v-card-text>
                <div class="caption">High resolution imagery can be purchased as number of searches. 
                  In some cases due to the size of the parcel, you could be charged more for an individual search. 
                  In those cases you will be informed before a search is executed on your behalf.</div>
                <v-radio-group v-model="highresOptionSelection">
      <v-radio
        v-for="item in highresOptions"
        :key="item.value"
        :label="item.text"
        :value="item.value"
      ></v-radio>
    </v-radio-group>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn  text @click="exhaustedDialog = false">Cancel</v-btn>
                <v-btn color="primary"  @click="addFunds" :loading="addFundsLoading">Submit</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>

    <v-dialog overlay-opacity="0" id="signup-dialog" v-if="signupDialog" v-model="signupDialog" persistent
              max-width="400">
        <v-card>
            <v-card-title>
                <v-carousel cycle height="165" hide-delimiters :show-arrows="false">
                    <v-carousel-item src="/images/aerial1.jpg">

                    </v-carousel-item>
                    <v-carousel-item src="/images/aerial2.jpg">

                    </v-carousel-item>
                    <v-carousel-item src="/images/aerial3.jpg">

                    </v-carousel-item>
                </v-carousel>
            </v-card-title>
            <v-card-text>
                <div class="title">Subscribe</div>
                <div class="subtitle-1">Subscription to Sitefotos Required
                </div>
                <div class="body-2 pt-4">Access to high resolution imagery is restricted to paid accounts. Please click
                    on subscribe to go to the billing page and create an active subscription with sitefotos.
                </div>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="green darken-1" text @click="signupDialog = false">Cancel</v-btn>
                <v-btn color="green darken-1" text @click="subscribe">Subscribe</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
    <v-dialog overlay-opacity="0" id="subscribe-dialog" v-if="subscribeDialog" v-model="subscribeDialog" persistent max-width="1200">
      <subscription v-on:signup-success="signupSuccess()" v-on:dismiss-dialog="cancelSubscribe()"></subscription>
    </v-dialog>
    <v-dialog overlay-opacity="0" :hide-overlay="$vuetify.breakpoint.smAndDown" width="80vw"
              transition="dialog-bottom-transition" :fullscreen="$vuetify.breakpoint.smAndDown" id="selection-dialog"
              v-if="selectionDialog" v-model="selectionDialog" persistent
    >
        <v-card>
            <v-toolbar class="elevation-0 white">
                <v-toolbar-title>
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon @click="selectionDialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>

            <v-card-text>
                <v-row>
                    <v-col cols="12" sm="12" md="6" lg="6">Purchased Imagery</v-col>

                    <v-col cols="12" sm="12" md="6" lg="6">Available Imagery</v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" sm="12" md="6" lg="6">
                        <v-simple-table dense fixed-header height="30vh" class="highres-dialog-tables">
                            <template v-slot:default>
                                <thead>
                                <tr>
                                    <th class="text-left">Provider</th>
                                    <th class="text-left">Date</th>
                                    <th class="text-left">Coverage</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="item in cachedImagery" :key="item.id" @click="selectCached(item)">
                                    <td>{{(item.tiles_provider === 'eagleview' ? 'EagleView' : item.tiles_provider === 'nearmap' ? 'Nearmap' : item.tiles_provider) || 'Nearmap'}}</td>
                                    <td> {{item.displayDate}}</td>
                                    <td> {{item.displayCoverage}}</td>
                                </tr>
                                </tbody>
                            </template>
                        </v-simple-table>
                    </v-col>
                    <v-col cols="12" sm="12" md="6" lg="6">
                        <v-simple-table dense fixed-header height="30vh" class="highres-dialog-tables">
                            <template v-slot:default>
                                <thead>
                                <tr>
                                    <th class="text-left">Provider</th>
                                    <th class="text-left">Date</th>
                                    <th class="text-left">Quality</th>
                                    <th class="text-left">Coverage</th>
                                    <th class="text-left">Price</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="item in cachedHighResResults" :key="item.id" @click="selectDownload(item)">
                                    <template v-if="item.provider === 'nearmap'">
                                        <td>Nearmap</td>
                                        <td>{{ item.displayDate }}</td>
                                        <td>{{ 11 - Math.round(item.resolution) }}</td>


                                        <td>Full</td>


                                        <td>&#36;{{item.estimate}}</td>
                                    </template>
                                    <template v-else-if="item.provider === 'eagleview'">
                                       <td>EagleView</td>
                                       <td>{{ item.displayDate }}</td>
                                       <td>{{ item.resolution }}</td>
                                       <td>Full</td>
                                       <td>&#36;{{ item.cost }}</td>
                                    </template>
                                </tr>
                                </tbody>
                            </template>
                        </v-simple-table>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" class="subtitle-2">Balance Remaining: $ {{remainQuota}}</v-col>

                </v-row>
                <v-row>
                    <v-col cols="12" class="caption">
                        <div>* Click to select from cached or available imagery.</div>
                        <div>* Selecting from available imagery requires an active subscription. If you are not
                            subscribed you will be asked to subscribe and deposit funds.
                        </div>
                        <div>* Quality of tiles is an estimate. Higher is better. 10 is the highest and 1 is the lowest
                            quality.
                        </div>

                    </v-col>
                </v-row>

            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn text @click="selectionDialog = false">Cancel</v-btn>
                <v-btn color="primary " @click="highresExhausted">Add more funds</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
    <v-overlay
            :opacity="0.1"
            :value="loader"
    >
        <v-progress-circular indeterminate size="64">
            Loading...
        </v-progress-circular>
    </v-overlay>
       <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
                    :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">{{
            snackbar.text }}
            <v-btn color="pink" text @click="snackbar.snackbar = false">Close</v-btn>
        </v-snackbar>
        <UtilityLib ref="utilityLib"></UtilityLib>
</div>`,
    components: {
        UtilityLib,
        subscription,
    },
    props: ['estimator', 'marginTop'],
    watch: {

        currentBaseLayer: function (newLayerID, oldLayerID) {
            this.ignoreClick = true;
            if (typeof newLayerID != 'undefined' && typeof oldLayerID != 'undefined') {
                if (newLayerID == 4)
                    this.changeHighRes(newLayerID, oldLayerID)
                else
                    this.changeRegularLayer(newLayerID, oldLayerID);
            }
            this.ignoreClick = false;
        },
       
    },
    data: function () {
        return {
            eagleViewTokens: {},
            subscribeDialog: false,
            ignoreClick: false,
            loader: false,
            urltiles: "https://tiles.sitefotos.com/api/tiles/gettile?accesscode=$accesscode&bid=$bid&bounds=$bounds&worker=$worker&client=$client&dt=$dt",
            urldownload: "https://tiles.sitefotos.com/api/tiles/downloadtile?token=$token",
            urlsearchcached: "https://tiles.sitefotos.com/api3/tiles/searchcached?accesscode=$accesscode&bounds=$bounds",
            urlsearch: "https://tiles.sitefotos.com/api3/tiles/search?accesscode=$accesscode&bounds=$bounds&worker=$worker",
            urlsearchcachedbyurl: "https://tiles.sitefotos.com/api3/tiles/searchcachedbyurlmapbuilder?accesscode=$accesscode&url=$url&bounds=$bounds",
            urlgetcached: "https://tiles.sitefotos.com/api3/tiles/getcachedtile?accesscode=$accesscode&worker=$worker&tileid=$tileid&bid=$bid&mid=$mid",
            urlquota: "https://tiles.sitefotos.com/api3/general/getquota?accesscode=$accesscode",
            urldownloadnearmap: "https://tiles.sitefotos.com/api3/tiles/downloadnearmaptile?accesscode=$accesscode&bounds=$bounds&date=$date&cost=$cost&worker=$worker&bid=$bid&mid=$mid",
            urleagleviewcapabilities: "https://tiles.sitefotos.com/api3/tiles/eagleview/capabilities?bounds=$bounds",
            addFundsLoading: false,
            snackbar: {
                snackbar: false,
                y: 'bottom',
                x: 'left',
                mode: '',
                timeout: 2000,
                text: ''
            },
            prepaid: false,
            highresOptionSelection: 25,
            highresOptions: [
                {
                    value: 25,
                    text: "25 Searches for $25",
                },
                {
                    value: 100,
                    text: "100 Searches for $100",
                },
                {
                    value: 250,
                    text: "250 Searches for $250",
                },
                {
                    value: 500,
                    text: "500 Searches for $500",
                },
                {
                    value: 1000,
                    text: "1000 Searches for $1000",
                }
            ]
        };
    },

    async mounted() {

        const layer = this.baseLayers.find(element => element.id == this.currentBaseLayer);
        if (map != null) {
            for (let index = 0; index < this.baseLayers.length; index++) {
                if (map.hasLayer(this.baseLayers[index].layerRef) && this.baseLayers[index].id != layer.id) {
                    map.removeLayer(this.baseLayers[index].layerRef)
                }
            }
        }

        let quota = await this.getQuota();
        this.updateQuota(quota);

    },
    created() {
        window.globalBaseLayerControl = this;
    },
    activated() {
        console.log("Baselayer Control Activated");
    },


    methods: {
        signupSuccess() {
            this.subscribeDialog = false;
            this.exhaustedDialog = true;
        },
        clickHighRes: function (currentBaseLayer) {
            if (currentBaseLayer == 4) {
                this.changeHighRes(4, 4)
            }
        },
        subscribe: function () {
            this.signupDialog = false;
            this.subscribeDialog = true;
        },
        cancelSubscribe: function () {
            this.subscribeDialog = false;
        },
        async addFunds() {
            const msg = `Please confirm that your credit card will be charged $${this.highresOptionSelection} for ${this.highresOptionSelection} searches.`;
            const result = await this.$refs.utilityLib.open("Confirm", msg, "Confirm", "Cancel", true);
            if (!result) {
                return;
            }
            this.addFundsLoading = true;
            const args = await this.tryCharge(this.highresOptionSelection);
            if (args == 1) {
                const quota = await this.getQuota();
                this.updateQuota(quota);
                await this.searchHighRes();
                this.exhaustedDialog = false;
                this.selectionDialog = true;
            }
            if (args == 2) {
                this.snackbar.text = "Sorry! There has been error, please contact support."
                this.snackbar.snackbar = true;
            }
            if (args == 3) {
                this.snackbar.text = "Sorry! There seems to be a problem with the password provided. Please try again after changing the password."
                this.snackbar.snackbar = true;
            }
            this.loader = false;
            this.addFundsLoading = false;
        },
        getCurrentBaseLayer() {
            const layer = this.baseLayers.find(element => element.id == this.currentBaseLayer);
            return layer.name;
        },
        selectNearMap: function (item) {
            // Enhanced quota checking for /api3 system
            if (this.remainQuota - item.cost >= 0) {
                this.stopPicMode();
                this.loader = true

                this.downloadNearmap(item).then(
                    function (result) {
                        if (result.url != null) {
                            this.tileUrl = result.url;
                            this.tileProvider = result.provider;
                            // Handle null dates for EagleView records
                            if (result.date && result.date !== null) {
                                if (result.date.indexOf('T') == -1) {
                                    this.tileDate = result.date
                                    this.tileDateDisplay = moment(
                                        result.date).format(
                                        "MM/DD/YY");
                                } else {
                                    this.tileDate = result.date
                                        .substring(0, result.date
                                            .indexOf('T'));
                                    this.tileDateDisplay = moment(
                                        result.date.substring(0,
                                            result.date.indexOf(
                                                'T'))).format(
                                        "MM/DD/YY");
                                }
                            } else {
                                this.tileDate = "N/A";
                                this.tileDateDisplay = "N/A";
                            }

                            // Show purchase confirmation with cost (enhanced for /api3)
                            if (result.cost > 0) {
                                this.snackbar.text = `Nearmap imagery purchased for $${result.cost}. Remaining quota: $${result.quota}`;
                                this.snackbar.snackbar = true;
                            }
                        }
                        this.loader = false
                    }.bind(this))
                    .catch(function(error) {
                        // Enhanced error handling for /api3 quota enforcement
                        console.error("Nearmap download failed:", error);
                        if (error.message && error.message.includes('quota')) {
                            this.snackbar.text = "Insufficient quota for this purchase. Please add more funds.";
                            this.highresExhausted();
                        } else {
                            this.snackbar.text = "Failed to download imagery. Please try again.";
                        }
                        this.snackbar.snackbar = true;
                        this.loader = false;
                    }.bind(this));
            } else if (this.payingCustomer == false && this.prepaid == false) {
                this.displaySignup();
            } else {
                this.highresExhausted()
            }
        },
        highresExhausted() {
            this.signupDialog = false;
            this.selectionDialog = false;
            this.exhaustedDialog = true;
        },
        selectDownload(item) {
            this.switchLayer(4, 0);
            if (item.provider == 'nearmap') {
                this.selectNearMap(item);
            } else if (item.provider == 'eagleview') {
                this.selectEagleView(item);
            } else if (item.provider == 'eagleview') {
                this.selectEagleView(item);
            }
            this.selectionDialog = false;
        },
        selectEagleView: async function (item) {
            if (this.overlay != null) {
                if (map.hasLayer(this.overlay)) {
                    map.removeLayer(this.overlay);
                    this.overlay = null;
                }
            }
            this.stopPicMode();

            // Purchase the layer first with /api3 quota enforcement
            try {
                this.loader = true;
                const area = turf.area(L.rectangle(this.getValidBounds()).toGeoJSON(15));
                const paddingarea = ((area + 400) / area) - 1;
                const boundsDG = this.getValidBounds().pad(paddingarea);

                const south = boundsDG.getSouth();
                const west = boundsDG.getWest();
                const north = boundsDG.getNorth();
                const east = boundsDG.getEast();

                const polygonBounds = [[west, south], [east, south], [east, north], [west, north], [west, south]];
                const purchaseResult = await this.purchaseEagleViewLayer(store.get('map/accessCode'), item.id, polygonBounds, item.tileMatrixSet, item.date, item.minZoom, item.maxZoom);

                // Get the JWT token for this layer
                const token = this.eagleViewTokens[item.id];
                const tileUrl = `/crux/eagleview/tile/${item.id}/{z}/{x}/{y}.jpeg?token=${token}`;

                this.overlay = L.tileLayer.maskedOffscreen(tileUrl, {
                    attribution: '&copy; EagleView',
                    minZoom: item.minZoom,
                    maxZoom: item.maxZoom,
                    tileBounds: boundsDG,
                    tileSize: 256
                }).addTo(map);

                this.tileUrl = tileUrl;
                this.tileProvider = 'eagleview';
                this.tileDate = item.date;
                this.tileDateDisplay = moment(item.date).format("MM/DD/YY");

                // Store EagleView specific metadata for saving
                store.set('baselayer/eagleviewPurchaseId', purchaseResult.purchaseId);
                store.set('baselayer/eagleviewLayerId', item.id);
                store.set('baselayer/eagleviewTileMatrixSet', item.tileMatrixSet);
                store.set('baselayer/eagleviewMinZoom', item.minZoom);
                store.set('baselayer/eagleviewMaxZoom', item.maxZoom);

                // Show success message with cost information (new in /api3)
                if (purchaseResult.cost > 0) {
                    this.snackbar.text = `EagleView layer purchased for $${purchaseResult.cost}. Remaining quota: $${purchaseResult.remainingQuota}`;
                    this.snackbar.snackbar = true;
                }
            } catch (error) {
                console.error("Failed to purchase and display EagleView layer:", error);

                // Enhanced error handling for /api3 quota enforcement
                if (error.message.includes('Insufficient quota')) {
                    this.snackbar.text = error.message + ". Please add more funds.";
                    this.highresExhausted(); // Show add funds dialog
                } else {
                    this.snackbar.text = "Failed to purchase layer. Please try again.";
                }
                this.snackbar.snackbar = true;
            } finally {
                this.loader = false;
            }
        },
        purchaseEagleViewLayer: async function(accesscode, layer, bounds, tileMatrixSet, imageryDate, minZoom, maxZoom) {
            const response = await fetch('https://tiles.sitefotos.com/api3/tiles/eagleview/purchase', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    accesscode,
                    layer,
                    bounds,
                    tileMatrixSet,
                    imageryDate,
                    minZoom,
                    maxZoom,
                }),
            });

            // Handle the new /api3 response format with quota enforcement
            if (!response.ok) {
                if (response.status === 402) {
                    // Insufficient quota - new error handling for /api3
                    const errorData = await response.json();
                    throw new Error(`Insufficient quota: Required $${errorData.required}, Available $${errorData.available}`);
                }
                throw new Error(`Failed to purchase layer: ${response.status}`);
            }

            const result = await response.json();

            // Store the JWT token for this layer
            this.eagleViewTokens[layer] = result.token;

            // Update remaining quota from the response (new in /api3)
            if (result.remainingQuota !== undefined) {
                this.remainQuota = result.remainingQuota;
            }

            // Log purchase details (new in /api3)
            console.log(`EagleView purchase successful: Cost $${result.cost}, Reason: ${result.reason}`);

            return result;
        },
        generateEagleViewToken: async function(purchaseIdOrLayer, tileMatrixSet) {
            // SIMPLIFIED: Support both ID-based (new) and layer-based (legacy) requests
            const isNumericId = !isNaN(purchaseIdOrLayer) && purchaseIdOrLayer !== null;

            const requestBody = {
                accesscode: store.get('map/accessCode'),
                tileMatrixSet: tileMatrixSet || 'GoogleMapsCompatible_7-22'
            };

            if (isNumericId) {
                // NEW: ID-based approach
                requestBody.purchaseId = purchaseIdOrLayer;
            } else {
                // LEGACY: Layer-based approach
                requestBody.layer = purchaseIdOrLayer;
            }

            const response = await fetch('https://tiles.sitefotos.com/api3/tiles/eagleview/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                throw new Error(`Failed to generate token: ${response.status}`);
            }

            return await response.json();
        },

        displayEagleViewCached: async function(item, tokenResponse) {
            const token = tokenResponse.token;
            console.log("Displaying cached EagleView imagery:", item);
            console.log("Using token:", token);
            if (this.overlay != null) {
                if (map.hasLayer(this.overlay)) {
                    map.removeLayer(this.overlay);
                    this.overlay = null;
                }
            }

            const bounds = L.latLngBounds(tokenResponse.bounds.map(([lng, lat]) => [lat, lng]));    

            // Store the token in eagleViewTokens for consistency
            this.eagleViewTokens[item.layer_id] = token;

            // Create EagleView tile layer URL with token (same /crux/ pattern as purchase)
            const tileUrl = `/crux/eagleview/tile/${item.layer_id}/{z}/{x}/{y}.jpeg?token=${token}`;

            // Use zoom levels from database (stored during purchase)
            const minZoom = item.min_zoom || 7;
            const maxZoom = item.max_zoom || 22;
             this.switchLayer(4, 0);
            // Add masked tile layer for EagleView
            this.overlay = L.tileLayer.maskedOffscreen(tileUrl, {
                attribution: '&copy; EagleView',
                minZoom: minZoom,
                maxZoom: maxZoom,
                tileBounds: bounds,
                tileSize: 256
            }).addTo(map);

            this.stopPicMode();
            this.tileUrl = tileUrl;
            this.tileProvider = 'eagleview';

            // Handle date display
            if (item.date && item.date !== null) {
                if (item.date.indexOf('T') === -1) {
                    this.tileDate = item.date;
                    this.tileDateDisplay = moment(item.date).format("MM/DD/YY");
                } else {
                    this.tileDate = item.date.substring(0, item.date.indexOf('T'));
                    this.tileDateDisplay = moment(item.date.substring(0, item.date.indexOf('T'))).format("MM/DD/YY");
                }
            }

            // SIMPLIFIED: Store both purchase ID and layer ID for compatibility
            store.set('baselayer/eagleviewPurchaseId', item.purchase_id);
            store.set('baselayer/eagleviewLayerId', item.layer_id);
            store.set('baselayer/eagleviewTileMatrixSet', item.tile_matrix_set);
            store.set('baselayer/eagleviewMinZoom', minZoom);
            store.set('baselayer/eagleviewMaxZoom', maxZoom);
        },
        async selectCached(item) {
            this.loader = true;

            // Check if this is an EagleView purchase (has layer_id but no storage_url)
            if (item.tiles_provider === 'eagleview' && item.layer_id) {
                try {
                    // SIMPLIFIED: Use purchase ID for ID-based communication if available
                    const identifier = item.purchase_id || item.layer_id; // Prefer purchase ID, fallback to layer ID

                    // Generate token for existing EagleView purchase
                    const tokenResponse = await this.generateEagleViewToken(identifier, item.tile_matrix_set);

                    // Display EagleView layer using the token
                    await this.displayEagleViewCached(item, tokenResponse);


                    this.fileOpenTrigger = false;
                    this.selectionDialog = false;
                } catch (error) {
                    console.error("Failed to load cached EagleView layer:", error);
                    this.snackbar.text = "Failed to load cached EagleView imagery. Please try again.";
                    this.snackbar.snackbar = true;
                }
            } else {
                // Handle regular cached tiles (Nearmap, etc.)
                let downloadResponse = await this.downloadCached(item.id);
                if (typeof downloadResponse.error != 'undefined') {
                    if (downloadResponse.error == false) {
                        this.switchLayer(4, 0);
                        this.displayImagery(downloadResponse);
                        this.fileOpenTrigger = false;
                        this.selectionDialog = false;
                    }
                }
            }
            this.loader = false;
        },
        revertToLoaded: async function () {
            store.set('baselayer/currentBaseLayer', 4)
            store.set('baselayer/fileOpenTrigger', true)
        },
        changeHighRes: async function (newLayerID, oldLayerID) {
            this.loader = true;
            if (!this.getValidBounds().isValid()) {
                this.snackbar.text = 'Please draw some items on the map before switching to high resolution imagery'
                this.snackbar.snackbar = true;
                this.loader = false;
                this.currentBaseLayer = oldLayerID;
                return;
            }
            let cacheResponse = await this.getCachedImagery();
            const accessCode = store.get('map/accessCode');
            const testArray = cacheResponse.filter(element => {
                const acc = element.tiles_accesscode.split(',')
                if (acc.includes(accessCode)) 
                    return element;
            })

            if (!this.payingCustomer && !this.fileOpenTrigger && testArray.length == 0 && this.prepaid == false) {
                console.log('No account')
                this.loader = false;
                this.signupDialog = true;
                return;
            }

            if (this.fileOpenTrigger) {
                // Check if this is an EagleView file opening
                if (this.tileProvider === 'eagleview' && store.get('baselayer/eagleviewLayerId')) {
                    try {
                        console.log("Opening EagleView file with layer ID:", store.get('baselayer/eagleviewLayerId'));

                        // SIMPLIFIED: Use purchase ID if available, fallback to layer ID
                        const purchaseId = store.get('baselayer/eagleviewPurchaseId');
                        const layerId = store.get('baselayer/eagleviewLayerId');
                        const tileMatrixSet = store.get('baselayer/eagleviewTileMatrixSet');
                        const identifier = purchaseId || layerId;
                        const tokenResponse = await this.generateEagleViewToken(identifier, tileMatrixSet);

                        console.log("Generated token for file opening:", tokenResponse);

                        // Switch to highres layer first
                        this.switchLayer(newLayerID, oldLayerID);

                        // Remove any existing overlay
                        if (this.overlay != null) {
                            if (map.hasLayer(this.overlay)) {
                                map.removeLayer(this.overlay);
                                this.overlay = null;
                            }
                        }

                        // Create tile URL
                        const tileUrl = `/crux/eagleview/tile/${layerId}/{z}/{x}/{y}.jpeg?token=${tokenResponse.token}`;
                        console.log("Creating EagleView tile layer with URL pattern:", tileUrl);

                        const latLngs = tokenResponse.bounds.map(([lng, lat]) => [lat, lng]);


                        const bounds = L.latLngBounds(latLngs);

                        // Create EagleView tile layer using zoom levels from file
                        const minZoom = store.get('baselayer/eagleviewMinZoom');
                        const maxZoom = store.get('baselayer/eagleviewMaxZoom');

                        if (!minZoom || !maxZoom) {
                            throw new Error('Missing zoom level information from file metadata');
                        }

                        this.overlay = L.tileLayer.maskedOffscreen(tileUrl, {
                            attribution: '&copy; EagleView',
                            minZoom: minZoom,
                            maxZoom: maxZoom,
                            tileBounds: bounds,
                            tileSize: 256
                        }).addTo(map);

                        console.log("Added EagleView overlay to map");

                        // Update component state
                        this.stopPicMode();
                        this.tileUrl = tileUrl;
                        this.tileProvider = 'eagleview';
                        this.eagleViewTokens[layerId] = tokenResponse.token;

                        this.fileOpenTrigger = false;
                        this.loader = false;
                        return;
                    } catch (error) {
                        console.error("Failed to load EagleView layer from file:", error);
                        this.snackbar.text = "Failed to load EagleView imagery from file. Please try again.";
                        this.snackbar.snackbar = true;
                        this.fileOpenTrigger = false;
                        this.loader = false;
                        return;
                    }
                }

                if (this.tileUrl == null) {
                    cacheResponse = _.sortBy(cacheResponse, { prop: "coverage", desc: true, });
                    if(cacheResponse.length === 0) {
                        this.exhaustedDialog = true;
                        this.loader = false;
                        return;
                    }
                    let downloadResponse = await this.downloadCached(cacheResponse[0].id)
                    if (typeof downloadResponse.error != 'undefined') {
                        if (downloadResponse.error == false) {
                            this.switchLayer(newLayerID, oldLayerID);
                            this.displayImagery(downloadResponse)
                            this.fileOpenTrigger = false;
                        }
                    }
                } else {
                    let img = _.find(cacheResponse, { 'tiles_url': this.tileUrl });
                    if (typeof img != 'undefined') {
                        
                        
                        
                                this.switchLayer(newLayerID, oldLayerID);
                                this.displayImageryUrl(cacheResponse)
                                this.fileOpenTrigger = false;
                            
                        
                    } else {
                        Vue.nextTick(function () {
                            this.fileOpenTrigger = false;
                            if (this.overlay != null) ;
                            {
                                if (map.hasLayer(this.overlay)) {
                                    map.removeLayer(this.overlay);
                                    this.overlay = null;
                                }
                            }
                            this.stopPicMode();
                            store.commit('baselayer/clearData')
                            this.currentBaseLayer = oldLayerID
                        }.bind(this))
                    }

                }
                this.loader = false;
            } else {
                this.loader = true;
                if (this.getValidBounds().isValid()) {
                   
                    let hasCached = cacheResponse.length > 0;
                    if (hasCached || this.remainQuota > 0 || store.get('map/takeoffLink') == true) {
                        let p1 = await this.searchHighRes();
                        this.selectionDialog = true;
                    } else {
                        this.exhaustedDialog = true;
                        this.loader=false;
                        return;
                    }
                } else {
                    Vue.nextTick(function () {
                        this.fileOpenTrigger = false;
                        if (this.overlay != null) ;
                        {
                            if (map.hasLayer(this.overlay)) {
                                map.removeLayer(this.overlay);
                                this.overlay = null;
                            }
                        }
                        this.stopPicMode();
                        store.commit('baselayer/clearData')
                        this.currentBaseLayer = oldLayerID
                        this.exhaustedDialog = true;
                    }.bind(this))
                    this.snackbar.text = 'Please draw some items on the map before switching to high resolution imagery'
                    this.snackbar.snackbar = true;

                }
                this.loader = false;
            }

        },
        displayImagery(downloadResponse) {
            let imageUrl = downloadResponse.url;
            if (typeof downloadResponse.bounds == 'string')
                var imageBounds = L.geoJSON(JSON.parse(downloadResponse.bounds))
                    .getBounds();
            else
                var imageBounds = L.geoJSON(downloadResponse.bounds).getBounds();

            if (this.overlay != null) {
                if (map.hasLayer(this.overlay)) {
                    map.removeLayer(this.overlay);
                    this.overlay = null;
                }
            }
            this.overlay = L.imageOverlay(imageUrl, imageBounds)
            this.overlay.addTo(map);
            this.stopPicMode();
            this.remainQuota = downloadResponse.quota;
            this.tileUrl = downloadResponse.url;
            this.tileProvider = downloadResponse.provider;
            // Handle null dates for EagleView records
            if (downloadResponse.date && downloadResponse.date !== null) {
                this.tileDate = downloadResponse.date.substring(0,
                    downloadResponse.date.indexOf('T'));
                this.tileDateDisplay = moment(downloadResponse.date
                    .substring(0, downloadResponse.date.indexOf(
                        'T'))).format("MM/DD/YY");
            } else {
                this.tileDate = "N/A";
                this.tileDateDisplay = "N/A";
            }

        },
        displayImageryUrl(downloadResponse) {
            downloadResponse = downloadResponse[0]
            let imageUrl = downloadResponse.tiles_url;
            
            if (typeof downloadResponse.bounds == 'string')
                var imageBounds = L.geoJSON(JSON.parse(downloadResponse.bounds))
                    .getBounds();
            else
                var imageBounds = L.geoJSON(downloadResponse.bounds).getBounds();

            if (this.overlay != null) {
                if (map.hasLayer(this.overlay)) {
                    map.removeLayer(this.overlay);
                    this.overlay = null;
                }
            }
            this.overlay = L.imageOverlay(imageUrl, imageBounds)
            this.overlay.addTo(map);
            this.stopPicMode();
            this.remainQuota = downloadResponse.quota;
            this.tileUrl = imageUrl;
            this.tileProvider = downloadResponse.provider;
            // Handle null dates for EagleView records
            if (downloadResponse.date && downloadResponse.date !== null) {
                this.tileDate = downloadResponse.date.substring(0,
                    downloadResponse.date.indexOf('T'));
                this.tileDateDisplay = moment(downloadResponse.date
                    .substring(0, downloadResponse.date.indexOf(
                        'T'))).format("MM/DD/YY");
            } else {
                this.tileDate = "N/A";
                this.tileDateDisplay = "N/A";
            }
        },
        switchLayer: function (newLayerID, oldLayerID) {
            const oldLayer = this.baseLayers.find(element => element.id == oldLayerID);
            let newLayer = this.baseLayers.find(element => element.id == newLayerID);
            for (let i = 0; i < this.baseLayers.length; i++) {
                if (map.hasLayer(this.baseLayers[i].layerRef))
                    map.removeLayer(this.baseLayers[i].layerRef)
            }
            if(newLayerID==2)
            {
                newLayer = this.baseLayers.find(element => element.id == 1);
            }
            map.addLayer(newLayer.layerRef)

        },
        changeRegularLayer: function (newLayerID, oldLayerID) {
            console.info(`Changing baselayer from ${oldLayerID} to ${newLayerID}`)
            this.fileOpenTrigger = false;
            if (this.overlay != null) 
            {
                console.info("Removing overlay layer")
                if (map.hasLayer(this.overlay)) {
                    map.removeLayer(this.overlay);
                    this.overlay = null;
                }
            }
            this.stopPicMode();
            store.commit('baselayer/clearData')
            this.switchLayer(newLayerID, oldLayerID);

        },

        tryCharge: async function (data) {
            const formBody = new URLSearchParams();
            formBody.append("accessCode", store.get('map/accessCode'));
            formBody.append("data", data);
            return await (await fetch(myBaseURL + '/vpics/mapcharge2', {
                method: 'POST',
                body: formBody,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })).text();
        },
        downloadCached: async function (tileid) {

            let myurl = this.urlgetcached;

            myurl = myurl.replace('$accesscode', store.get('map/takeoffLink') == true ? store.get('map/takeoffClientAccessCode') : store.get('map/accessCode'));
            myurl = myurl.replace('$tileid', tileid);
            myurl = myurl.replace('$worker', store.get('map/takeoffLink') == true ? 1 : 0);
            myurl = myurl.replace('$bid', store.get('map/buildingID'));
            myurl = myurl.replace('$mid', store.get('map/fileID'));
            return await (await fetch(myurl)).json();


        },
        stopPicMode: function () {
            this.picMode = false;
            //Added for MAPBUILDER-K Issue in Sentry
            //TODO: Find out why was it undefined in the first place.
            if (typeof map.contextmenu != 'undefined')
                map.contextmenu.removeItem(3)
        },

        downloadNearmap: async function (dataset) {
            var myurl = this.urldownloadnearmap;
            var area = turf.area(L.rectangle(this.getValidBounds()).toGeoJSON(15))
            var paddingarea = ((area + 400) / area) - 1;

            var boundsDG = this.getValidBounds().pad(paddingarea);
            myurl = myurl.replace('$bounds', boundsDG.toBBoxString());
            myurl = myurl.replace('$accesscode', store.get('map/takeoffLink') == true ? store.get('map/takeoffClientAccessCode') : store.get('map/accessCode'));
            myurl = myurl.replace('$date', dataset.date);
            myurl = myurl.replace('$worker', store.get('map/takeoffLink') == true ? 1 : 0);
            myurl = myurl.replace('$bid', store.get('map/buildingID'));
            myurl = myurl.replace('$mid', store.get('map/fileID'));
            myurl = myurl.replace('$cost', dataset.cost);

            // Enhanced error handling for /api3 quota enforcement
            const response = await fetch(myurl);
            if (!response.ok) {
                if (response.status === 402) {
                    const errorData = await response.json();
                    throw new Error(`Insufficient quota: Required $${errorData.required}, Available $${errorData.available}`);
                }
                throw new Error(`Failed to download Nearmap imagery: ${response.status}`);
            }

            var data = await response.json();

            if (data.url != null) {
                this.remainQuota = data.quota;
                var imageUrl = data.url;
                if (typeof data.bounds == 'string')
                    var imageBounds = L.geoJSON(JSON.parse(data.bounds))
                        .getBounds();
                else
                    var imageBounds = L.geoJSON(data.bounds).getBounds();

                if (this.overlay != null) {
                    if (map.hasLayer(this.overlay)) {
                        map.removeLayer(this.overlay);
                        this.overlay = null; // Fixed: was using == instead of =
                    }
                }

                this.overlay = L.imageOverlay(imageUrl, imageBounds)
                this.overlay.addTo(map);
                //this._picData = data.search;

            }
            return data;

        },
        getQuota: async function () {
            const url = this.urlquota.replace('$accesscode', store.get('map/accessCode'));
            const data = await (await fetch(url)).json();
            return data;
        },
        getCachedImagery: async function () {
            let area = turf.area(L.rectangle(this.getValidBounds()).toGeoJSON(15))
            let paddingarea = ((area + 400) / area) - 1;

            let boundsDG = this.getValidBounds().pad(paddingarea);

            let myurl = this.urlsearchcached;
            if(this.fileOpenTrigger && this.tileUrl != null && this.tileUrl != '') {
                myurl = this.urlsearchcachedbyurl
                myurl = myurl.replace('$url', this.tileUrl);
            }
            myurl = myurl.replace('$accesscode', store.get('map/accessCode'));

            myurl = myurl.replace('$bounds', boundsDG.toBBoxString());
            let data = await (await fetch(myurl)).json();

            if (data.length > 0) {
                data = _.uniq(data, 'id');
                data = _.sortBy(data, 'date').reverse();
            }
            for (var i = 0; i < data.length; i++) {
                // Handle null dates for EagleView records
                if (data[i].date && data[i].date !== null) {
                    data[i].displayDate = moment(data[i].date.substring(0,
                        data[i].date.indexOf('T'))).format("MM/DD/YY");
                } else {
                    data[i].displayDate = "N/A";
                }
                data[i].displayCoverage = ((data[i].coverage) *
                    100).toFixed(2) + '%'
            }
            this.cachedImagery = data
            return data;
        },
        getValidBounds() {
            // Create temporary featureGroup to calculate bounds
            const tempGroup = L.featureGroup();
            const presets = globalMapManager.getPresets();
            // Add all layers from presets, regardless of visibility
            for (const preset in presets) {
                if (presets[preset].leafletlayer && 
                    Object.keys(presets[preset].leafletlayer._layers).length > 0) {
                    
                    for (const layer of Object.keys(presets[preset].leafletlayer._layers)) {
                        
                        if (presets[preset].leafletlayer._layers[layer].feature && presets[preset].leafletlayer._layers[layer].feature.properties.area ) {

                            if(presets[preset].leafletlayer._layers[layer]._bounds) {
                                tempGroup.addLayer(presets[preset].leafletlayer._layers[layer]);
                            }
                            

                        }
                    }
                }
            }
           
            return tempGroup.getBounds();
        },
        searchHighRes: async function () {
            let area = turf.area(L.rectangle(this.getValidBounds()).toGeoJSON(15))
            let paddingarea = ((area + 400) / area) - 1;

            let boundsDG = this.getValidBounds().pad(paddingarea);
            if (boundsDG.toBBoxString() == this.cachedBounds && this.cachedHighResResults != null) {
                return this.cachedHighResResults;
            } else {
                this.cachedBounds = boundsDG.toBBoxString();
                let myurl = this.urlsearch;

                myurl = myurl.replace('$accesscode', store.get('map/accessCode'));
                myurl = myurl.replace('$worker', store.get('map/takeoffLink') == true ? 1 : 0);
                myurl = myurl.replace('$bounds', boundsDG.toBBoxString());
                let data = await (await fetch(myurl)).json();
                let sResults = [];
                if (typeof data.nearsearch.im != 'undefined') {
                    for (var i = 0; i < data.nearsearch.im
                        .length; i++) {
                        data.nearsearch.im[i].displayDate = moment(data.nearsearch.im[i].date).format("MM/DD/YY");
                        data.nearsearch.im[i].formattedDate = moment(data.nearsearch.im[i].date).format("YYYY-MM-DD");
                        data.nearsearch.im[i].estimate = data.nearsearch.estimate
                        data.nearsearch.im[i].cost = data.nearsearch.estimate
                        data.nearsearch.im[i].tileid = data.nearsearch.im[i].tile
                        data.nearsearch.im[i].provider = 'nearmap';
                        sResults.push(data.nearsearch.im[i])

                    }
                }
               if (data.eaglesearch) {
                    data.eaglesearch.forEach(layer => {
                        sResults.push({
                            provider: 'eagleview',
                            id: layer.id,
                            displayDate: moment(layer.date).format("MM/DD/YY"),
                            date: layer.date,
                            resolution: layer.resolution,
                            isContained: layer.isContained,
                            cost: layer.cost,
                            bounds: layer.bounds,
                            minZoom: layer.minZoom,
                            maxZoom: layer.maxZoom,
                            tileMatrixSet: layer.tileMatrixSet
                        });
                    });
                }
                if (sResults.length > 0)
                    sResults = _.sortBy(sResults, ['date']).reverse();
                this.cachedHighResResults = sResults;
                return sResults;
            }
        },
        updateQuota: function (data) {

            if (typeof data.allocation == 'undefined') {
                this.remainQuota = 0;
                this.prepaid = false;
            } else {
                if (data.allocation == null || data.allocation == 0) {
                    this.remainQuota = 0;
                    this.prepaid = false;
                } else {
                    this.remainQuota = data.allocation - data.usage;
                    if(this.remainQuota > 0)
                        this.prepaid = true
                }

            }


        }
    },

    computed: {
        ...VuexPathify.sync('baselayer', [
            'currentBaseLayer',
            'remainQuota',
            'picMode',
            'tileDate',
            'signupDialog',
            'tileProvider',
            'tileUrl',
            'fileOpenTrigger',
            'overlay',
            'exhaustedDialog',
            'tileDateDisplay',
            'selectionDialog',
            'cachedHighResResults',
            'cachedBounds',
            'cachedImagery'
        ]),
        baseLayers: VuexPathify.get('baselayer/baseLayers'),
        payingCustomer: VuexPathify.get('map/payingCustomer'),
    }
}
